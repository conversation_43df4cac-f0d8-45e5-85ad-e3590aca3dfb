// pages/shop/detail/detail.js
import shopService from '../../../services/shopService';
import evaluationService from '../../../services/evaluationService';

Page({
  /**
   * 页面的初始数据
   */
  data: {
    shopId: null,
    shopInfo: null,
    pets: [],
    evaluations: [],
    loading: true,
    currentTab: 'pets',  // 当前选中的标签，可选值：'pets'、'info'、'evaluations'
    pageSize: 10,
    showContentInput: false,  // 控制内容输入模态框
    tempContent: '',          // 临时存储输入内容
    petCurrentPage: 1,
    evalCurrentPage: 1,
    petHasMore: true,
    evalHasMore: true,
    evalTotal: 0,  // 评价总数
    // 评价弹窗相关
    showEvaluationModal: false,
    evaluationForm: {
      rating: 0,
      content: ''
    },
    submittingEvaluation: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('📄 商店详情页面加载，参数:', options);

    const { id } = options;
    // 检查是否有ID参数
    if (!id) {
      console.error('❌ 缺少商店ID参数');
      wx.showToast({
        title: '商店ID不存在',
        icon: 'none',
        duration: 2000
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 2000);
      return;
    }

    // 确保shopId是数字类型
    const shopId = parseInt(id);
    if (isNaN(shopId) || shopId <= 0) {
      console.error('❌ 商店ID格式无效:', id);
      wx.showToast({
        title: '商店ID格式错误',
        icon: 'none',
        duration: 2000
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 2000);
      return;
    }

    console.log('✅ 商店ID验证通过:', shopId);

    this.setData({
      shopId:shopId,
      loading: true
    });

    // 获取商店详情
    this.getShopDetail();

    // 获取商店宠物列表
    this.getShopPets();

    // 获取商店评价
    this.getShopEvaluations();
  },

  /**
   * 获取商店详情
   */
  getShopDetail() {
    const { shopId } = this.data;

    // 这里应该使用之前定义的shopService.getShopDetail方法，但似乎没有提供
    // 暂时使用getShopList过滤出当前商店
    shopService.getShopList({ id: shopId })
      .then(res => {
        if (res && res.data && Array.isArray(res.data)) {
          const shop = res.data.find(item => item.id == shopId);
          if (shop) {
            this.setData({
              shopInfo: shop,
              loading: false
            });
          } else {
            this.handleError('未找到商店信息');
          }
        } else {
          this.handleError('获取商店详情失败');
        }
      })
      .catch(err => {
        console.error('获取商店详情失败', err);
        this.handleError('获取商店详情失败');
      });
  },

  /**
   * 获取商店宠物列表
   */
  getShopPets(loadMore = false) {
    const { shopId, petCurrentPage, pageSize, pets } = this.data;

    // 验证shopId
    if (!shopId || !Number.isInteger(shopId) || shopId <= 0) {
      console.error('❌ 商店ID无效:', shopId);
      wx.showToast({
        title: '商店ID无效',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    if (!loadMore) {
      this.setData({ loading: true });
    }

    // 计算要请求的页码
    const targetPage = loadMore ? petCurrentPage + 1 : 1;

    console.log('🐾 获取宠物列表，页码:', targetPage, '每页:', pageSize);

    shopService.getShopPets(shopId, {
      page: targetPage,
      pageSize: pageSize
    })
      .then(res => {
        console.log('✅ 宠物列表响应:', res);

        if (res && res.code === 200) {
          const newPets = Array.isArray(res.data) ? res.data : [];

          // 处理宠物数据 - 确保数据格式正确
          const processedPets = newPets.map(pet => {
            // 确保所有必要字段都存在
            return {
              id: pet.id || pet.ID || Math.random().toString(36).substr(2, 9),
              ID: pet.ID || pet.id,
              name: pet.name || `${pet.breed || '可爱'}宠物`,
              breed: pet.breed || '未知品种',
              age: pet.age || 0,
              gender: pet.gender || '未知',
              price: pet.price || 0,
              image: pet.image || pet.photo || '/assets/images/default-pet.png',
              photo: pet.photo || pet.image || '/assets/images/default-pet.png',
              stock: pet.stock || 0,
              description: pet.description || `可爱的${pet.breed || '宠物'}，健康活泼。`
            };
          });

          this.setData({
            pets: loadMore ? [...pets, ...processedPets] : processedPets,
            petHasMore: processedPets.length >= pageSize, // 如果返回数据等于pageSize，可能还有更多数据
            petCurrentPage: targetPage, // 更新当前页码
            loading: false
          });

          console.log('✅ 宠物数据更新完成，当前页:', targetPage, '数据数量:', processedPets.length);
        } else {
          console.warn('⚠️ 响应格式异常:', res);
          this.setData({
            petHasMore: false,
            loading: false
          });

          if (!loadMore && (!res || !res.data || res.data.length === 0)) {
            wx.showToast({
              title: '暂无宠物信息',
              icon: 'none',
              duration: 2000
            });
          }
        }
      })
      .catch(err => {
        console.error('🔥 获取宠物列表失败:', err);
        this.setData({
          petHasMore: false,
          loading: false
        });

        // 根据错误类型显示不同的提示
        let errorMessage = '获取宠物列表失败';
        if (err.message) {
          if (err.message.includes('网络')) {
            errorMessage = '网络连接失败，请检查网络';
          } else if (err.message.includes('参数')) {
            errorMessage = '参数错误';
          } else if (err.message.includes('商店ID')) {
            errorMessage = err.message;
          } else {
            errorMessage = err.message;
          }
        }

        // 只在非加载更多的情况下显示错误提示
        if (!loadMore) {
          wx.showToast({
            title: errorMessage,
            icon: 'none',
            duration: 2000
          });
        }
      });
  },

  /**
   * 获取商店评价 - 使用修改后的评价服务接口
   */
  getShopEvaluations(loadMore = false) {
    const { shopId, evalCurrentPage, pageSize, evaluations } = this.data;

    // 验证shopId
    if (!shopId || !Number.isInteger(shopId) || shopId <= 0) {
      console.error('❌ 商店ID无效:', shopId);
      wx.showToast({
        title: '商店ID无效',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    if (!loadMore) {
      this.setData({ loading: true });
    }

    console.log('📝 获取商店评价，参数:', {
      page: evalCurrentPage,
      pageSize: pageSize,
      id: shopId
    });

    // 使用修改后的评价接口 - POST请求，传入正确的参数格式
    evaluationService.getShopEvaluations({
      page: evalCurrentPage,
      pageSize: pageSize,
      id: shopId  // 注意：接口文档中参数名是 id，不是 shopId
    })
      .then(res => {
        console.log('✅ 商店评价响应:', res);

        if (res && res.code === 200) {
          const newEvaluations = Array.isArray(res.data) ? res.data : [];

          // 处理评价数据 - 接口已经在service层处理过了，这里只需要基本映射
          const processedEvaluations = newEvaluations.map(evaluation => {
            // 验证rating范围
            let rating = evaluation.rating || 0;
            if (rating < 1 || rating > 5) {
              console.warn('⚠️ 评分超出范围:', rating);
              rating = Math.max(1, Math.min(5, rating)); // 修正到1-5范围
            }

            return {
              id: evaluation.id || Math.random().toString(36).substr(2, 9),
              userName: evaluation.username || evaluation.userName || '匿名用户', // 兼容两种字段名
              userAvatar: evaluation.avatar || evaluation.userAvatar || '', // 头像已在service中处理过路径
              content: evaluation.content || '用户未填写评价内容',
              rating: rating,
              createTime: evaluation.createTime || evaluation.time || '未知时间'
            };
          });

          this.setData({
            evaluations: loadMore ? [...evaluations, ...processedEvaluations] : processedEvaluations,
            evalHasMore: newEvaluations.length >= pageSize,
            evalCurrentPage: evalCurrentPage + (loadMore ? 1 : 0),
            evalTotal: res.total || 0,  // 保存评价总数
            loading: false
          });

          console.log('✅ 评价数据处理完成，总数:', res.total, '当前页数据:', processedEvaluations.length);
        } else {
          console.warn('⚠️ 响应格式异常:', res);
          this.setData({
            evalHasMore: false,
            loading: false
          });
        }
      })
      .catch(err => {
        console.error('🔥 获取评价失败:', err);
        this.setData({
          evalHasMore: false,
          loading: false
        });

        // 根据错误类型显示不同的提示
        let errorMessage = '获取评价失败';
        if (err.message) {
          if (err.message.includes('网络')) {
            errorMessage = '网络连接失败，请检查网络';
          } else if (err.message.includes('参数')) {
            errorMessage = '参数错误';
          } else {
            errorMessage = err.message;
          }
        }

        wx.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 2000
        });
      });
  },

  /**
   * 切换标签
   */
  switchTab(e) {
    const { tab } = e.currentTarget.dataset;
    this.setData({ currentTab: tab });
  },

  /**
   * 加载更多宠物
   */
  loadMorePets() {
    if (this.data.petHasMore && !this.data.loading) {
      this.getShopPets(true);
    }
  },

  /**
   * 加载更多评价
   */
  loadMoreEvaluations() {
    if (this.data.evalHasMore && !this.data.loading) {
      this.getShopEvaluations(true);
    }
  },

  /**
   * 预约购买宠物
   */
  reservePet(e) {
    const { petId } = e.currentTarget.dataset;
    const { shopId } = this.data;

    // 检查是否登录
    const token = wx.getStorageSync('token');
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none',
        duration: 2000
      });

      setTimeout(() => {
        wx.navigateTo({
          url: '/pages/login/login'
        });
      }, 1500);

      return;
    }

    // 验证参数
    if (!shopId || !Number.isInteger(shopId) || shopId <= 0) {
      wx.showToast({
        title: '商店信息错误',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    if (!petId) {
      wx.showToast({
        title: '宠物信息错误',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 生成当前时间作为预购时间，格式：YYYY-MM-DD HH:mm:ss
    const now = new Date();
    const paymentTime = now.getFullYear() + '-' +
      String(now.getMonth() + 1).padStart(2, '0') + '-' +
      String(now.getDate()).padStart(2, '0') + ' ' +
      String(now.getHours()).padStart(2, '0') + ':' +
      String(now.getMinutes()).padStart(2, '0') + ':' +
      String(now.getSeconds()).padStart(2, '0');

    // 显示确认对话框
    wx.showModal({
      title: '确认预约',
      content: '确定要预约这只宠物吗？',
      success: (res) => {
        if (res.confirm) {
          this.performReservation(shopId, petId, paymentTime);
        }
      }
    });
  },

  /**
   * 执行预约操作
   */
  performReservation(storeID, petID, paymentTime) {
    // 显示加载提示
    wx.showLoading({
      title: '预约中...',
      mask: true
    });

    // 调用预约接口
    shopService.reservePet({
      storeID: storeID,  // 注意：接口文档中是storeID
      petID: petID,      // 注意：接口文档中是petID
      paymentTime: paymentTime
    })
      .then(res => {
        wx.hideLoading();

        if (res && res.code === 200) {
          wx.showToast({
            title: res.message || '预约成功',
            icon: 'success',
            duration: 2000
          });

          // 可以选择刷新宠物列表或跳转到订单页面
          setTimeout(() => {
            // 方案1：刷新当前页面的宠物列表
            this.getShopPets();

            // 方案2：跳转到订单查看页面（如果有的话）
            // wx.navigateTo({
            //   url: '/pages/order/list/list'
            // });
          }, 1500);
        } else {
          throw new Error(res.message || '预约失败');
        }
      })
      .catch(err => {
        wx.hideLoading();
        console.error('🔥 预约失败:', err);

        let errorMessage = '预约失败，请重试';
        if (err.message) {
          if (err.message.includes('登录')) {
            errorMessage = '请先登录';
          } else if (err.message.includes('网络')) {
            errorMessage = '网络连接失败';
          } else {
            errorMessage = err.message;
          }
        }

        wx.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 2000
        });
      });
  },

  /**
   * 评价商店 - 显示评价弹窗
   */
  evaluateShop() {
    // 检查是否登录
    const token = wx.getStorageSync('token');
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none',
        duration: 2000
      });

      setTimeout(() => {
        wx.navigateTo({
          url: '/pages/login/login'
        });
      }, 1500);

      return;
    }

    // 显示评价弹窗
    this.setData({
      showEvaluationModal: true,
      evaluationForm: {
        rating: 0,
        content: ''
      }
    });
  },

  /**
   * 关闭评价弹窗
   */
  closeEvaluationModal() {
    this.setData({
      showEvaluationModal: false
    });
  },

  /**
   * 评分变化
   */
  onRatingChange(e) {
    const { rating } = e.currentTarget.dataset;

    // 验证评分范围
    if (rating >= 1 && rating <= 5) {
      this.setData({
        'evaluationForm.rating': rating
      });
    }
  },

  /**
   * 提交评价 - 使用商店评价接口
   */
  submitEvaluation() {
    const { shopId, evaluationForm } = this.data;
    const { rating, content } = evaluationForm;

    // 验证shopId
    if (!shopId || !Number.isInteger(shopId) || shopId <= 0) {
      wx.showToast({
        title: '商店ID无效',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 验证评分
    if (!rating || rating < 1 || rating > 5) {
      wx.showToast({
        title: '请选择1-5星评分',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 验证评价内容
    if (!content || !content.trim()) {
      wx.showToast({
        title: '请输入评价内容',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    if (content.length > 500) {
      wx.showToast({
        title: '评价内容不能超过500字',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 设置提交状态
    this.setData({ submittingEvaluation: true });

    // 构造请求参数，根据接口文档格式
    const requestData = {
      storeID: shopId,  // 注意：商店评价接口中是storeID
      rating: rating,
      content: content.trim()
    };

    console.log('📝 提交商店评价，参数:', requestData);

    // 调用商店评价接口
    evaluationService.evaluateShop(requestData)
      .then(res => {
        if (res && res.code === 200) {
          wx.showToast({
            title: res.message || '评价成功',
            icon: 'success',
            duration: 2000
          });

          // 关闭弹窗
          this.setData({
            showEvaluationModal: false,
            submittingEvaluation: false
          });

          // 重新加载评价列表
          this.setData({
            evalCurrentPage: 1,
            evaluations: [] // 清空现有数据
          });
          this.getShopEvaluations();
        } else {
          throw new Error(res.message || '评价失败');
        }
      })
      .catch(err => {
        console.error('🔥 提交评价失败', err);

        let errorMessage = '评价失败，请重试';
        if (err.message) {
          if (err.message.includes('登录')) {
            errorMessage = '请先登录';
          } else if (err.message.includes('网络')) {
            errorMessage = '网络连接失败';
          } else {
            errorMessage = err.message;
          }
        }

        wx.showToast({
          title: errorMessage,
          icon: 'none',
          duration: 2000
        });
      })
      .finally(() => {
        this.setData({ submittingEvaluation: false });
      });
  },

  /**
   * 处理错误
   */
  handleError(message) {
    wx.showToast({
      title: message,
      icon: 'none',
      duration: 2000
    });

    this.setData({ loading: false });

    setTimeout(() => {
      wx.navigateBack();
    }, 2000);
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    // 重新加载所有数据
    this.setData({
      petCurrentPage: 1,
      evalCurrentPage: 1,
      pets: [],
      evaluations: []
    });

    Promise.all([
      this.getShopDetail(),
      this.getShopPets(),
      this.getShopEvaluations()
    ]).finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    const { currentTab } = this.data;

    if (currentTab === 'pets') {
      this.loadMorePets();
    } else if (currentTab === 'evaluations') {
      this.loadMoreEvaluations();
    }
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    const { shopInfo } = this.data;

    return {
      title: shopInfo ? `${shopInfo.name} - 宠物商店` : '宠物商店详情',
      path: `/pages/shop/detail/detail?id=${this.data.shopId}`,
      imageUrl: shopInfo?.image || '/assets/images/default-pet.png'
    };
  },

  /**
   * 打开内容输入模态框
   */
  openContentInput() {
    this.setData({
      showContentInput: true,
      tempContent: this.data.evaluationForm.content || ''
    });
  },

  /**
   * 关闭内容输入模态框
   */
  closeContentInput() {
    this.setData({
      showContentInput: false,
      tempContent: ''
    });
  },

  /**
   * 确认输入内容
   */
  confirmContentInput() {
    const { tempContent } = this.data;

    if (tempContent.length > 500) {
      wx.showToast({
        title: '内容不能超过500字',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    this.setData({
      'evaluationForm.content': tempContent,
      showContentInput: false,
      tempContent: ''
    });
  },

  /**
   * 选择快捷评价内容
   */
  selectQuickContent(e) {
    const { content } = e.currentTarget.dataset;
    this.setData({
      tempContent: content
    });
  },

  /**
   * 聚焦自定义输入（使用系统输入法）
   */
  focusCustomInput() {
    wx.showModal({
      title: '输入评价内容',
      placeholderText: '请输入您的评价...',
      editable: true,
      content: this.data.tempContent,
      success: (res) => {
        if (res.confirm) {
          const content = res.content || '';
          if (content.length <= 500) {
            this.setData({
              tempContent: content
            });
          } else {
            wx.showToast({
              title: '内容不能超过500字',
              icon: 'none',
              duration: 2000
            });
          }
        }
      }
    });
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 空函数，用于阻止事件冒泡
  },

})