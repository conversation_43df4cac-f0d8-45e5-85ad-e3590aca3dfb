/**
 * 救助站相关接口服务
 */
import request from './request';
import { CONFIG } from './config';

/**
 * 查询救助站（普通用户）
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码（从1开始）
 * @param {number} params.pageSize - 每页数量
 * @param {string} params.address - 查询地址（必需）
 * @returns {Promise<Object>} 救助站列表
 */
function getRescueStationList(params = {}) {
  console.log('📍 获取救助站列表，参数:', params);

  const { page = 1, pageSize = 10, address } = params;

  // 参数验证
  if (!Number.isInteger(page) || page < 1) {
    return Promise.reject(new Error('页码必须是大于0的整数'));
  }

  if (!Number.isInteger(pageSize) || pageSize < 1 || pageSize > 100) {
    return Promise.reject(new Error('每页数量必须是1-100之间的整数'));
  }

  if (!address || typeof address !== 'string') {
    return Promise.reject(new Error('地址(address)为必填项，且必须是字符串'));
  }

  const requestData = { page, pageSize, address };

  const options = {
    useMock: CONFIG.USE_MOCK,
    headers: {}
  };

  const token = wx.getStorageSync('token');
  if (token) {
    options.headers['Authorization'] = token;
  }

  return request.post(API_PATH.SEARCH_RESCUE_STATIONS, requestData, options)
    .then(response => {
      console.log('📥 救助站列表响应:', response);

      if (response && response.code === 200) {
        const result = {
          code: response.code,
          message: response.message || '查询成功',
          total: response.total || 0,
          data: Array.isArray(response.data)
            ? response.data.map(item => ({
                id: item.ID,
                name: item.name || '',
                address: item.address || '',
                contact: item.contact || '',
                license: item.license || '',
                photo: item.photo || ''
              }))
            : []
        };

        return result;
      } else {
        throw new Error(response.message || '获取救助站失败');
      }
    })
    .catch(error => {
      console.error('❌ 获取救助站列表失败:', error);

      if (error.message && error.message.includes('网络')) {
        return {
          code: 200,
          message: '网络连接失败，请检查网络后重试',
          total: 0,
          data: []
        };
      }

      throw error;
    });
}


/**
 * 查询救助站（普通用户）--已检查，严格按照接口文档
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码 (必需)
 * @param {number} params.pageSize - 每页数量 (必需)
 * @param {string} params.address - 地址 (必需)
 * @returns {Promise} 救助站列表
 * 接口: POST /users/common/search/station
 * 参数: page (integer), pageSize (integer), address (string)
 * 返回: {code: 200, message: "成功", total: 1, data: [{ID, address, name, contact, license, photo}]}
 */
function searchStations(params = {}) {
  console.log('🔍 查询救助站请求:', params);

  return new Promise((resolve, reject) => {
    try {
      // 参数验证 - 严格按照接口文档要求
      if (!params || typeof params !== 'object') {
        reject(new Error('查询参数不能为空'));
        return;
      }

      // 必填字段检查 - page
      if (!params.page) {
        reject(new Error('页码不能为空'));
        return;
      }

      if (typeof params.page !== 'number' || !Number.isInteger(params.page)) {
        reject(new Error('页码必须为整数'));
        return;
      }

      if (params.page <= 0) {
        reject(new Error('页码必须大于0'));
        return;
      }

      // 必填字段检查 - pageSize
      if (!params.pageSize) {
        reject(new Error('每页数量不能为空'));
        return;
      }

      if (typeof params.pageSize !== 'number' || !Number.isInteger(params.pageSize)) {
        reject(new Error('每页数量必须为整数'));
        return;
      }

      if (params.pageSize <= 0) {
        reject(new Error('每页数量必须大于0'));
        return;
      }

      // 必填字段检查 - address
      if (!params.address) {
        reject(new Error('地址不能为空'));
        return;
      }

      if (typeof params.address !== 'string') {
        reject(new Error('地址必须为字符串'));
        return;
      }

      if (params.address.trim() === '') {
        reject(new Error('地址不能为空白字符'));
        return;
      }

      // 🎯 直接使用本地Mock数据
      if (CONFIG.DEV_CONFIG?.USE_LOCAL_MOCK) {
        console.log('🎯 查询救助站使用本地Mock数据');
        resolve({
          code: CONFIG.ERROR_CODES.SUCCESS,
          message: "成功",
          total: 1,
          data: [
            {
              ID: 5,
              address: "湖南省 武安市 嵊州市 原桥72号 77号院",
              name: "爱心救助站",
              contact: "13800138000",
              license: "救助站许可证001",
              photo: "https://loremflickr.com/400/400?lock=35756093158543"
            }
          ]
        });
        return;
      }

      // 构造请求体数据 - 严格按照接口文档格式
      const requestData = {
        page: params.page,
        pageSize: params.pageSize,
        address: params.address.trim()
      };

      console.log('🔍 发送查询救助站请求（POST）:', requestData);

      // 准备请求选项
      const requestOptions = {
        useMock: CONFIG.USE_MOCK,
        showLoading: true
      };

      request.post(CONFIG.API_PATHS.SEARCH_RESCUE_STATIONS, requestData, requestOptions).then(result => {
        console.log('✅ 查询救助站响应:', result);

        // 使用配置的成功状态码
        if (result && result.code === CONFIG.ERROR_CODES.SUCCESS) {
          console.log('🎉 查询救助站成功');

          resolve({
            code: CONFIG.ERROR_CODES.SUCCESS,
            message: result.message || '成功',
            total: result.total || 0,
            data: result.data || []
          });
        } else {
          // API返回了错误状态
          const errorMsg = result?.message || '查询救助站失败，请稍后重试';
          reject(new Error(errorMsg));
        }
      }).catch(error => {
        console.error('🔥 查询救助站接口调用失败:', error);
        reject(error);
      });

    } catch (err) {
      console.error('❌ 查询救助站参数准备失败:', err);
      reject(new Error('查询救助站失败，请检查参数'));
    }
  });
}

/**
 * 评价救助站（普通用户）--已检查，严格按照接口文档
 * @param {Object} data - 评价数据
 * @param {number} data.stationId - 救助站ID编号 (必需)
 * @param {number} data.rating - 评分 (必需)
 * @param {string} data.content - 评价内容 (必需)
 * @returns {Promise} 评价结果
 * 接口: POST /users/common/evaluate/rescuestation
 * 参数: stationId (integer), rating (number), content (string), Authorization (Header, 可选)
 * 返回: {code: 35, message: "成功", data: {}}
 */
function evaluateRescueStation(data) {
  return request.post(API_PATHS.EVALUATE_RESCUE_STATION, data, {
    useMock: CONFIG.USE_MOCK,
    headers: {
      'Authorization': wx.getStorageSync('token') || ''
    }
  });
}

/**
 * 查看用户对救助站的评价（普通用户）--已检查，严格按照接口文档
 * @param {Object} data - 查询参数
 * @param {number} data.page - 页码 (必需)
 * @param {number} data.pageSize - 每页数量 (必需)
 * @param {number} data.id - 救助站ID (必需)
 * @returns {Promise} 评价列表
 * 接口: POST /users/common/rescuestation/evaluation
 * 参数: page (integer), pageSize (integer), id (integer), Authorization (Header, 可选)
 * 返回: {code: 5, message: "nulla dolore ut id", total: 1, data: {username, avatar, rating, content}}
 */
function getRescueStationEvaluations(data = {}) {
  console.log('🏠 查看用户对救助站的评价请求:', data);

  return new Promise((resolve, reject) => {
    try {
      // 参数验证 - 严格按照接口文档要求
      if (!data || typeof data !== 'object') {
        reject(new Error('查询参数不能为空'));
        return;
      }

      // 必填字段检查 - page
      if (!data.page) {
        reject(new Error('页码不能为空'));
        return;
      }

      if (typeof data.page !== 'number' || !Number.isInteger(data.page)) {
        reject(new Error('页码必须为整数'));
        return;
      }

      if (data.page <= 0) {
        reject(new Error('页码必须大于0'));
        return;
      }

      // 必填字段检查 - pageSize
      if (!data.pageSize) {
        reject(new Error('每页数量不能为空'));
        return;
      }

      if (typeof data.pageSize !== 'number' || !Number.isInteger(data.pageSize)) {
        reject(new Error('每页数量必须为整数'));
        return;
      }

      if (data.pageSize <= 0) {
        reject(new Error('每页数量必须大于0'));
        return;
      }

      // 必填字段检查 - id
      if (!data.id) {
        reject(new Error('救助站ID不能为空'));
        return;
      }

      if (typeof data.id !== 'number' || !Number.isInteger(data.id)) {
        reject(new Error('救助站ID必须为整数'));
        return;
      }

      // 获取token
      const token = wx.getStorageSync(CONFIG.STORAGE_KEYS.TOKEN);

      // 🎯 直接使用本地Mock数据
      if (CONFIG.DEV_CONFIG?.USE_LOCAL_MOCK) {
        console.log('🎯 查看救助站评价使用本地Mock数据');
        resolve({
          code: 5,
          message: "查询成功",
          total: 1,
          data: {
            username: "张三",
            avatar: "https://avatars.githubusercontent.com/u/68187022",
            rating: "5",
            content: "服务很好，救助站工作人员很负责任"
          }
        });
        return;
      }

      // 构造请求体数据 - 严格按照接口文档格式
      const requestData = {
        page: data.page,
        pageSize: data.pageSize,
        id: data.id
      };

      console.log('🏠 发送查看救助站评价请求（POST）:', requestData);

      // 准备请求选项
      const requestOptions = {
        useMock: CONFIG.USE_MOCK,
        showLoading: true
      };

      // 如果有token，添加Authorization头部（可选）
      if (token) {
        requestOptions.headers = {
          'Authorization': token
        };
      }

      request.post(CONFIG.API_PATHS.GET_RESCUE_EVALUATIONS, requestData, requestOptions).then(result => {
        console.log('✅ 查看救助站评价响应:', result);

        // 使用配置的成功状态码
        if (result && result.code === 5) {
          console.log('🎉 查看救助站评价成功');

          resolve({
            code: 5,
            message: result.message || '查询成功',
            total: result.total || 0,
            data: result.data || {}
          });
        } else {
          // API返回了错误状态
          const errorMsg = result?.message || '查看救助站评价失败，请稍后重试';
          reject(new Error(errorMsg));
        }
      }).catch(error => {
        console.error('🔥 查看救助站评价接口调用失败:', error);
        reject(error);
      });

    } catch (err) {
      console.error('❌ 查看救助站评价参数准备失败:', err);
      reject(new Error('查看救助站评价失败，请检查参数'));
    }
  });
}

/**
 * 删除对救助站的评价（普通用户）--已检查，严格按照接口文档
 * @param {number} id - 评价ID (必需)
 * @returns {Promise} 删除结果
 * 接口: DELETE /users/common/rescuestation/delete/{id}
 * 参数: id (path, integer), Authorization (Header, 可选)
 * 返回: {code: 200, message: "删除成功", data: {}}
 */
function deleteRescueStationEvaluation(id) {
  console.log('🗑️ 删除对救助站的评价请求:', id);

  return new Promise((resolve, reject) => {
    try {
      // 参数验证 - 严格按照接口文档要求
      if (!id) {
        reject(new Error('评价ID不能为空'));
        return;
      }

      if (typeof id !== 'number' && typeof id !== 'string') {
        reject(new Error('评价ID必须为数字或字符串'));
        return;
      }

      // 确保是整数类型
      if (!Number.isInteger(Number(id))) {
        reject(new Error('评价ID必须为整数'));
        return;
      }

      // 转换为字符串确保路径参数正确
      const evaluationId = String(id);

      // 获取token
      const token = wx.getStorageSync(CONFIG.STORAGE_KEYS.TOKEN);

      // 🎯 直接使用本地Mock数据
      if (CONFIG.DEV_CONFIG?.USE_LOCAL_MOCK) {
        console.log('🎯 删除救助站评价使用本地Mock数据');
        resolve({
          code: CONFIG.ERROR_CODES.SUCCESS,
          message: '删除成功',
          data: {}
        });
        return;
      }

      // 准备请求选项
      const requestOptions = {
        useMock: CONFIG.USE_MOCK,
        showLoading: true
      };

      // 如果有token，添加Authorization头部（可选）
      if (token) {
        requestOptions.headers = {
          'Authorization': token
        };
      }

      console.log('🏠 发送删除救助站评价请求（DELETE）:', evaluationId);

      request.delete(`${CONFIG.API_PATHS.DELETE_RESCUE_EVALUATION}/${evaluationId}`, {}, requestOptions).then(result => {
        console.log('✅ 删除救助站评价响应:', result);

        // 使用配置的成功状态码
        if (result && result.code === CONFIG.ERROR_CODES.SUCCESS) {
          console.log('🎉 删除救助站评价成功');

          resolve({
            code: CONFIG.ERROR_CODES.SUCCESS,
            message: result.message || '删除成功',
            data: result.data || {}
          });
        } else {
          // API返回了错误状态
          const errorMsg = result?.message || '删除救助站评价失败，请稍后重试';
          reject(new Error(errorMsg));
        }
      }).catch(error => {
        console.error('🔥 删除救助站评价接口调用失败:', error);
        reject(error);
      });

    } catch (err) {
      console.error('❌ 删除救助站评价参数准备失败:', err);
      reject(new Error('删除救助站评价失败，请检查参数'));
    }
  });
}


/**
 * 上传流浪动物信息（救助站用户）
 * @param {Object} data - 动物信息
 * @returns {Promise} 上传结果
 */
function uploadAnimalInfo(data) {
  return request.post(API_PATH.UPLOAD_ANIMAL_INFO, data, { 
    useMock: CONFIG.USE_MOCK,
    headers: {
      'Authorization': wx.getStorageSync('token') || ''
    }
  });
}

/**
 * 获取单个动物信息（救助站）
 * @param {number} animalId - 动物ID
 * @returns {Promise} 动物信息
 */
function getAnimalInfo(animalId) {
  if (!animalId || !Number.isInteger(Number(animalId)) || Number(animalId) <= 0) {
    return Promise.reject(new Error('动物ID必须是大于0的整数'));
  }
  
  return request.get(`/api/users/rescuestation/animals/${animalId}`, {}, { 
    useMock: CONFIG.USE_MOCK,
    headers: {
      'Authorization': wx.getStorageSync('token') || ''
    }
  });
}

/**
 * 获取流浪动物信息（救助站用户）
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码，从1开始
 * @param {number} params.pageSize - 每页数量，建议5-100
 * @returns {Promise} 动物信息列表
 */
function getAnimalsForRescue(params = {}) {
  const { page = 1, pageSize = 10 } = params;

  // 校验页码
  if (!Number.isInteger(page) || page < 1) {
    return Promise.reject(new Error('页码必须是大于0的整数'));
  }

  // 校验每页数量
  if (!Number.isInteger(pageSize) || pageSize < 5 || pageSize > 100) {
    return Promise.reject(new Error('每页数量必须是5-100之间的整数'));
  }

  // 使用POST请求并传递请求体参数
  return request.post(API_PATH.GET_ANIMALS, { page, pageSize }, { 
    useMock: CONFIG.USE_MOCK,
    headers: {
      'Authorization': wx.getStorageSync('token') || '' // 获取授权token
    }
  });
}

/**
 * 修改流浪动物信息（救助站用户）
 * @param {Object} data - 动物信息
 * @returns {Promise} 修改结果
 */
function updateAnimalInfo(data) {

  // 校验数据
  if (!data || typeof data !== 'object') {
    return Promise.reject(new Error('动物信息数据不能为空'));
  }

  const { id, breed, gender, status } = data;

  // 校验动物ID
  if (!id || !Number.isInteger(Number(id)) || Number(id) <= 0) {
    return Promise.reject(new Error('动物ID必须是大于0的整数'));
  }

  // 校验动物品种
  if (!breed || typeof breed !== 'string' || breed.trim().length === 0) {
    return Promise.reject(new Error('动物品种不能为空'));
  }

  if (breed.length > 50) {
    return Promise.reject(new Error('动物品种不能超过50字符'));
  }

  // 校验性别
  if (!gender || (gender !== '男' && gender !== '女')) {
    return Promise.reject(new Error('性别必须是"男"或"女"'));
  }

  // 校验动物状态
  if (!status || typeof status !== 'string' || status.trim().length === 0) {
    return Promise.reject(new Error('动物状态不能为空'));
  }

  // 校验来源
  if (data.source && data.source.length > 100) {
    return Promise.reject(new Error('动物来源不能超过100字符'));
  }

  // 校验医疗记录
  if (data.medicalRecord && data.medicalRecord.length > 500) {
    return Promise.reject(new Error('医疗记录不能超过500字符'));
  }

  // 校验出生日期格式
  if (data.birthDate) {
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(data.birthDate)) {
      return Promise.reject(new Error('出生日期格式必须是YYYY-MM-DD'));
    }
  }

  // 发送PUT请求更新动物信息
  return request.put(API_PATH.UPDATE_ANIMAL_INFO, data, { 
    useMock: CONFIG.USE_MOCK,
    headers: {
      'Authorization': wx.getStorageSync('token') || ''
    }
  });
}

/**
 * @deprecated 请使用 updateAnimalInfo 方法
 */
function updateAnimal(data) {
  console.warn('updateAnimal 方法已废弃，请使用 updateAnimalInfo 方法');
  return updateAnimalInfo(data);
}

/**
 * 查看领养匹配状态（救助站用户）
 * @param {Object} params - 查询参数
 * @returns {Promise} 领养匹配状态列表
 */
function getAdoptStatusForRescue(params = {}) {
  return request.get(API_PATH.VIEW_ADOPT_STATUS, params, { 
    useMock: CONFIG.USE_MOCK,
    headers: {
      'Authorization': wx.getStorageSync('token') || ''
    }
  });
}

/**
 * 修改领养匹配状态（救助站）
 * @param {Object} data - 更新数据
 * @returns {Promise} 更新结果
 */
function updateAdoptStatus(data, id, status) {
  // 替换路径中的 ID 和 status 参数
  const url = API_PATH.UPDATE_ADOPT_STATUS.replace('{ID}', id).replace('{status}', status);
  
  return request.put(url, data, { 
    useMock: CONFIG.USE_MOCK,
    headers: {
      'Authorization': wx.getStorageSync('token') || ''  // 获取授权token
    }
  });
}

/**
 * 查看用户评价（救助站用户）
 * @param {Object} params - 查询参数
 * @returns {Promise} 评价列表
 */
function getEvaluationsForRescue(params = { page: 1, pageSize: 10 }) {
  return request.post(API_PATH.VIEW_EVALUATIONS_FOR_RESCUE, params, { 
    useMock: CONFIG.USE_MOCK,
    headers: {
      'Authorization': wx.getStorageSync('token') || ''  // 获取授权token
    }
  });
}

export default {
  getRescueStationList,
  searchStations,        
  evaluateRescueStation,
  getRescueStationEvaluations,
  deleteRescueStationEvaluation,
  uploadAnimalInfo,
  getAnimalInfo,
  getAnimalsForRescue,
  updateAnimalInfo,
  updateAnimal,
  getAdoptStatusForRescue,
  updateAdoptStatus,
  getEvaluationsForRescue
};