/**
 * 评价相关接口服务 - 根据API文档优化
 */
import request from './request';
import { CONFIG } from './config';

/**
 * 评价宠物商店（普通用户）
 * @param {Object} data - 评价数据
 * @param {number} data.storeID - 宠物商店ID
 * @param {number} data.rating - 评分，范围1-5
 * @param {string} data.content - 评价内容，最大长度500字符
 * @returns {Promise} 评价结果
 */
function evaluateShop(data) {
  // 验证必需参数
  if (!data.storeID) {
    return Promise.reject(new Error('商店ID不能为空'));
  }
  
  if (!data.rating || data.rating < 1 || data.rating > 5) {
    return Promise.reject(new Error('评分必须在1-5之间'));
  }
  
  if (!data.content || !data.content.trim()) {
    return Promise.reject(new Error('评价内容不能为空'));
  }
  
  if (data.content.length > 500) {
    return Promise.reject(new Error('评价内容不能超过500字'));
  }

  // 确保请求包含认证头部
  const token = wx.getStorageSync('token');
  if (!token) {
    return Promise.reject(new Error('请先登录'));
  }

  // 构造请求数据
  const requestData = {
    storeID: data.storeID,
    rating: data.rating,
    content: data.content.trim()
  };

  // 发送请求
  return request.post('/users/common/evaluateshop', requestData, { 
    useMock: CONFIG.USE_MOCK,
    headers: {
      'Authorization': token
    }
  }).then(response => {
    if (response && response.code === 200) {
      return response;
    } else if (response && response.code === 500) {
      throw new Error(response.message || '服务器错误');
    } else {
      throw new Error(response.message || '未知错误');
    }
  }).catch(error => {
    console.error('评价商店失败:', error);
    throw error;
  });
}

/**
 * 查看自己对宠物商店的评价（普通用户）- 根据后端API文档修改
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码，从1开始
 * @param {number} params.pageSize - 每页数量，建议10-100
 * @returns {Promise} 评价列表
 */
function getMyShopEvaluations(params = {}) {
  console.log('👤 获取自己对商店的评价，参数:', params);

  const { page = 1, pageSize = 10 } = params;

  // 参数验证
  if (!Number.isInteger(page) || page < 1) {
    console.error('❌ 页码参数无效:', page);
    return Promise.reject(new Error('页码必须是大于0的整数'));
  }

  if (!Number.isInteger(pageSize) || pageSize < 1 || pageSize > 100) {
    console.error('❌ 每页数量参数无效:', pageSize);
    return Promise.reject(new Error('每页数量必须是1-100之间的整数'));
  }

  const token = wx.getStorageSync('token');

  const requestData = {
    page,
    pageSize
  };

  const options = {
    useMock: CONFIG.USE_MOCK
  };

  if (token) {
    options.headers = {
      'Authorization': token
    };
  }

  console.log('📤 发送我的商店评价请求:', requestData);

  return request.post(API_PATH.GET_MY_SHOP_EVALUATIONS, requestData, options)
    .then(response => {
      console.log('📥 我的商店评价响应:', response);

      if (response && response.code === 200) {
        const result = {
          code: response.code,
          message: response.message || '查询成功',
          total: response.data?.length || 0,
          data: Array.isArray(response.data) ? response.data.map(evaluation => {
            let processedRating = evaluation.rating || 0;
            if (processedRating < 1 || processedRating > 5) {
              console.warn('⚠️ 评分超出范围，自动修正:', processedRating);
              processedRating = Math.max(1, Math.min(5, processedRating));
            }

            return {
              id: evaluation.ID,
              rating: processedRating,
              content: evaluation.content || '',
              username: evaluation.name || '匿名用户'
            };
          }) : []
        };

        console.log('✅ 我的商店评价数据处理完成:', result);
        return result;
      } else {
        console.error('❌ 接口响应错误:', response);
        throw new Error(response.message || '获取评价失败');
      }
    })
    .catch(error => {
      console.error('🔥 获取我的商店评价失败:', error);

      if (error.message && error.message.includes('网络')) {
        console.log('🌐 网络错误，返回空数据');
        return {
          code: 200,
          message: '网络连接失败，请检查网络后重试',
          total: 0,
          data: []
        };
      }

      throw error;
    });
}


/**
 * 查看用户对医院的评价（医院用户）
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码，从1开始
 * @param {number} params.pageSize - 每页数量，建议10-100
 * @param {number} params.hospitalID - 宠物医院ID，与查询宠物医院接口的ID对应
 * @returns {Promise} 评价列表
 */
function getHospitalEvaluations(params = {}) {
  console.log('🏥 获取医院评价，参数:', params);
  
  // 参数验证 - 严格按照接口文档要求
  const { page = 1, pageSize = 10, hospitalID } = params;
  
  // 验证必需参数 - hospitalID是必选参数
  if (!hospitalID) {
    console.error('❌ 医院ID不能为空');
    return Promise.reject(new Error('医院ID不能为空'));
  }
  
  // 验证参数类型和范围 - 确保为正整数
  if (!Number.isInteger(page) || page < 1) {
    console.error('❌ 页码参数无效:', page);
    return Promise.reject(new Error('页码必须是大于0的整数'));
  }
  
  if (!Number.isInteger(pageSize) || pageSize < 1 || pageSize > 100) {
    console.error('❌ 每页数量参数无效:', pageSize);
    return Promise.reject(new Error('每页数量必须是1-100之间的整数'));
  }
  
  if (!Number.isInteger(hospitalID) || hospitalID < 1) {
    console.error('❌ 医院ID参数无效:', hospitalID);
    return Promise.reject(new Error('医院ID必须是大于0的整数'));
  }

  // 获取认证令牌（可选，根据文档说明Authorization是可选的）
  const token = wx.getStorageSync('token');
  
  // 构造请求体数据 - 严格按照接口文档格式
  const requestData = {
    page: page,
    pageSize: pageSize,
    hospitalID: hospitalID  // 使用hospitalID而不是id
  };

  console.log('📤 发送医院评价请求（POST）:', requestData);

  // 构造请求选项
  const options = { 
    useMock: CONFIG.USE_MOCK
  };
  
  // 添加认证头部（如果有token）
  if (token) {
    options.headers = {
      'Authorization': token
    };
  }

  // 发送POST请求到正确的端点
  return request.post(API_PATH.GET_HOSPITAL_EVALUATIONS, requestData, options)
    .then(response => {
      console.log('📥 医院评价接口响应:', response);
      
      // 检查响应格式 - 按照接口文档处理
      if (response && response.code === 200) {
        // 处理响应数据
        const result = {
          code: response.code,
          message: response.message || '查询成功',
          total: response.total || 0,
          data: Array.isArray(response.data) ? response.data : []
        };
        
        // 处理评价数据 - 按照接口文档要求
        if (result.data.length > 0) {
          result.data = result.data.map(evaluation => {
            // 处理头像路径 - 按照文档要求
            let processedAvatar = evaluation.avatar || '';
            
            if (processedAvatar) {
              if (processedAvatar.startsWith('http')) {
                // 完整URL（http开头），直接使用
                console.log('✅ 头像使用完整URL:', processedAvatar);
              } else {
                // 相对路径，需拼接D:/images/
                // 按照文档：若为相对路径，需拼接D:/images/（如/api/images/avatar.jpg拼接为D:/images/api/images/avatar.jpg）
                if (processedAvatar.startsWith('/')) {
                  processedAvatar = `D:/images${processedAvatar}`;
                } else {
                  processedAvatar = `D:/images/${processedAvatar}`;
                }
                console.log('🔧 头像路径已拼接:', processedAvatar);
              }
            }
            
            // 确保rating在1-5范围内
            let processedRating = evaluation.rating || 0;
            if (processedRating && (processedRating < 1 || processedRating > 5)) {
              console.warn('⚠️ 评分超出范围，自动修正:', processedRating);
              processedRating = Math.max(1, Math.min(5, processedRating));
            }
            
            // 标准化字段名，保持与现有代码兼容
            return {
              id: evaluation.id || Math.random().toString(36).substr(2, 9),
              username: evaluation.username || '匿名用户', // 接口返回username
              userName: evaluation.username || '匿名用户', // 兼容现有代码的userName字段
              content: evaluation.content || '',
              avatar: processedAvatar,
              userAvatar: processedAvatar, // 兼容现有代码的userAvatar字段
              rating: processedRating,
              createTime: evaluation.createTime || evaluation.time || new Date().toISOString()
            };
          });
        }
        
        console.log('✅ 医院评价数据处理完成:', result);
        return result;
      } else if (response && response.code === 500) {
        console.error('❌ 服务器错误:', response.message);
        throw new Error(response.message || '服务器错误');
      } else {
        console.error('❌ 未知响应格式:', response);
        throw new Error(response.message || '获取评价失败');
      }
    })
    .catch(error => {
      console.error('🔥 获取医院评价失败:', error);
      
      // 网络错误的友好处理
      if (error.message && error.message.includes('网络')) {
        console.log('🌐 网络错误，返回空数据');
        return {
          code: 200,
          message: '网络连接失败，请检查网络后重试',
          total: 0,
          data: []
        };
      }
      
      throw error;
    });
}

/**
 * 评价医院（普通用户）
 * @param {Object} data - 评价数据
 * @param {number} data.hospitalID - 宠物医院ID
 * @param {number} data.rating - 评分，评分1-5
 * @param {string} data.content - 评价内容，最大长度500字符
 * @returns {Promise} 评价结果
 */
function evaluateHospital(data) {
  console.log('🏥 提交医院评价，参数:', data);

  // 🎯 模拟评价成功
  if (CONFIG.DEV_CONFIG?.USE_LOCAL_MOCK) {
    console.log('🎯 医院评价提交使用本地Mock数据');
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          code: 200,
          message: '评价成功',
          data: {
            id: Date.now(),
            success: true
          }
        });
      }, 800);
    });
  }

  // 验证必需参数
  if (!data.hospitalID) {
    return Promise.reject(new Error('医院ID不能为空'));
  }
  
  if (!data.rating || data.rating < 1 || data.rating > 5) {
    return Promise.reject(new Error('评分必须在1-5之间'));
  }
  
  if (!data.content || !data.content.trim()) {
    return Promise.reject(new Error('评价内容不能为空'));
  }
  
  if (data.content.length > 500) {
    return Promise.reject(new Error('评价内容不能超过500字'));
  }

  // 确保请求包含认证头部
  const token = wx.getStorageSync('token');
  if (!token) {
    return Promise.reject(new Error('请先登录'));
  }

  // 构造请求数据 - 严格按照API文档格式
  const requestData = {
    hospitalID: data.hospitalID,  // 使用hospitalID
    rating: data.rating,
    content: data.content.trim()
  };

  console.log('📤 发送医院评价提交请求:', requestData);

  // 发送请求
  return request.post('/users/common/evaluatehospital', requestData, {
    useMock: CONFIG.USE_MOCK,
    headers: {
      'Authorization': token
    }
  })
    .then(response => {
      console.log('📥 医院评价提交响应:', response);
      
      if (response && response.code === 200) {
        return response;
      } else if (response && response.code === 500) {
        throw new Error(response.message || '服务器错误');
      } else {
        throw new Error(response.message || '未知错误');
      }
    })
    .catch(error => {
      console.error('🔥 提交医院评价失败:', error);
      throw error;
    });
}

/**
 * 获取模拟评价数据 - 按照接口文档格式
 * @returns {Array} 模拟评价数据列表
 */
function getMockEvaluationData() {
  return [
    {
      id: 1,
      username: '爱猫人士',
      content: '店里的宠物都很健康，服务也很好，强烈推荐！工作人员非常专业，能够给出很好的建议。',
      avatar: '/avatars/user1.jpg', // 相对路径，会被拼接为 D:/images/avatars/user1.jpg
      rating: 5,
      createTime: '2024-03-15 14:30:25'
    },
    {
      id: 2,
      username: '小王',
      content: '价格合理，品种丰富，店员很专业。环境也很干净整洁。',
      avatar: '', // 空头像
      rating: 4,
      createTime: '2024-03-12 10:15:30'
    },
    {
      id: 3,
      username: '宠物达人',
      content: '环境很好，宠物都很活泼健康，值得信赖的好店铺。第二次来了，依然很满意。',
      avatar: 'https://example.com/avatar3.jpg', // 完整URL，直接使用
      rating: 5,
      createTime: '2024-03-10 16:45:15'
    },
    {
      id: 4,
      username: '新手铲屎官',
      content: '第一次买宠物，店员很耐心地介绍，很满意。后续还会再来的。',
      avatar: '/avatars/user4.png',
      rating: 4,
      createTime: '2024-03-08 11:20:45'
    },
    {
      id: 5,
      username: '旁熙瑶',
      content: 'tempor ea labore',
      avatar: '',
      rating: 4,
      createTime: '2024-03-05 09:15:20'
    }
  ];
}

/**
 * 评价救助站（普通用户）--已检查，严格按照接口文档
 * @param {Object} data - 评价数据
 * @param {number} data.stationId - 救助站ID (必需)
 * @param {number} data.rating - 评分 (必需)
 * @param {string} data.content - 评价内容 (必需)
 * @returns {Promise} 评价结果
 * 接口: POST /users/common/evaluate/rescuestation
 * 参数: stationId (integer), rating (number), content (string), Authorization (Header, 可选)
 * 返回: {code: 35, message: "成功", data: {}}
 */
function evaluateRescueStation(data) {
  console.log('🏠 评价救助站请求:', data);

  return new Promise((resolve, reject) => {
    try {
      // 参数验证 - 严格按照接口文档要求
      if (!data || typeof data !== 'object') {
        reject(new Error('评价数据不能为空'));
        return;
      }

      // 必填字段检查 - stationId
      if (!data.stationId) {
        reject(new Error('救助站ID不能为空'));
        return;
      }

      if (typeof data.stationId !== 'number' || !Number.isInteger(data.stationId)) {
        reject(new Error('救助站ID必须为整数'));
        return;
      }

      // 必填字段检查 - rating
      if (data.rating === null || data.rating === undefined) {
        reject(new Error('评分不能为空'));
        return;
      }

      if (typeof data.rating !== 'number') {
        reject(new Error('评分必须为数字'));
        return;
      }

      // 必填字段检查 - content
      if (!data.content) {
        reject(new Error('评价内容不能为空'));
        return;
      }

      if (typeof data.content !== 'string') {
        reject(new Error('评价内容必须为字符串'));
        return;
      }

      if (data.content.trim() === '') {
        reject(new Error('评价内容不能为空白字符'));
        return;
      }

      // 获取token
      const token = wx.getStorageSync(CONFIG.STORAGE_KEYS.TOKEN);

      // 🎯 直接使用本地Mock数据
      if (CONFIG.DEV_CONFIG?.USE_LOCAL_MOCK) {
        console.log('🎯 评价救助站使用本地Mock数据');
        resolve({
          code: 35,
          message: '成功',
          data: {}
        });
        return;
      }

      // 构造请求体数据 - 严格按照接口文档格式
      const requestData = {
        stationId: data.stationId,
        rating: data.rating,
        content: data.content.trim()
      };

      console.log('🏠 发送评价救助站请求（POST）:', requestData);

      // 准备请求选项
      const requestOptions = {
        useMock: CONFIG.USE_MOCK,
        showLoading: true
      };

      // 如果有token，添加Authorization头部（可选）
      if (token) {
        requestOptions.headers = {
          'Authorization': token
        };
      }

      request.post(CONFIG.API_PATHS.EVALUATE_RESCUE_STATION, requestData, requestOptions).then(result => {
        console.log('✅ 评价救助站响应:', result);

        // 使用配置的成功状态码
        if (result && result.code === 35) {
          console.log('🎉 评价救助站成功');

          resolve({
            code: 35,
            message: result.message || '成功',
            data: result.data || {}
          });
        } else {
          // API返回了错误状态
          const errorMsg = result?.message || '评价救助站失败，请稍后重试';
          reject(new Error(errorMsg));
        }
      }).catch(error => {
        console.error('🔥 评价救助站接口调用失败:', error);
        reject(error);
      });

    } catch (err) {
      console.error('❌ 评价救助站参数准备失败:', err);
      reject(new Error('评价救助站失败，请检查参数'));
    }
  });
}

/**
 * 查看自己对宠物医院的评价（普通用户）
 * 
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码（必填）
 * @param {number} params.pageSize - 每页条数（必填）
 * @returns {Promise} 评价列表
 */
function getSelfHospitalEvaluations(params = {}) {
  if (!params.page || !params.pageSize) {
    return Promise.reject(new Error('分页参数缺失'));
  }

  return request.post(API_PATHS.GET_SELF_HOSPITAL_EVALUATIONS, params, {
    useMock: CONFIG.USE_MOCK,
    headers: {
      'Authorization': wx.getStorageSync('token') || ''
    }
  });
}

/**
 * 查看自己对救助站的评价（普通用户）
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码（从1开始）
 * @param {number} params.pageSize - 每页数量（建议10-100）
 * @returns {Promise<Object>} 评价列表
 */
function getMyRescueStationEvaluations(params = {}) {
  console.log('🚑 获取自己对救助站的评价，参数:', params);

  const { page = 1, pageSize = 10 } = params;

  // 参数验证
  if (!Number.isInteger(page) || page < 1) {
    return Promise.reject(new Error('页码必须是大于0的整数'));
  }

  if (!Number.isInteger(pageSize) || pageSize < 1 || pageSize > 100) {
    return Promise.reject(new Error('每页数量必须是1-100之间的整数'));
  }

  const requestData = { page, pageSize };

  const options = {
    useMock: CONFIG.USE_MOCK,
    headers: {}
  };

  const token = wx.getStorageSync('token');
  if (token) {
    options.headers['Authorization'] = token;
  }

  return request.post(GET_MY_RESCUE_EVALUATIONS, requestData, options)
    .then(response => {
      console.log('📥 救助站评价接口响应:', response);

      if (response && response.code === 200) {
        const result = {
          code: response.code,
          message: response.message || '查询成功',
          total: response.total || 0,
          data: Array.isArray(response.data) ? response.data.map(item => {
            let rating = item.rating || 0;
            if (rating < 1 || rating > 5) {
              console.warn('⚠️ 评分超出范围，已修正:', rating);
              rating = Math.max(1, Math.min(5, rating));
            }

            return {
              id: item.ID,
              name: item.name || '匿名用户',
              content: item.content || '',
              rating
            };
          }) : []
        };

        return result;
      } else {
        throw new Error(response.message || '获取评价失败');
      }
    })
    .catch(error => {
      console.error('❌ 获取救助站评价失败:', error);

      if (error.message && error.message.includes('网络')) {
        return {
          code: 200,
          message: '网络连接失败，请检查网络后重试',
          total: 0,
          data: []
        };
      }

      throw error;
    });
}


/**
 * 删除对商店的评价（普通用户）
 * @param {string} evaluationId - 评价ID
 * @returns {Promise} 删除结果
 */
function deleteShopEvaluation(evaluationId) {
  return request.delete(`/users/common/shopevaluation/delete/${evaluationId}`, {}, { useMock: CONFIG.USE_MOCK });
}

/**
 * 删除对医院的评价（普通用户）
 * @param {string} evaluationId - 评价ID
 * @returns {Promise} 删除结果
 */
function deleteHospitalEvaluation(evaluationId) {
  return request.delete(`/users/common/hospitalevaluation/delete/${evaluationId}`, {}, { useMock: CONFIG.USE_MOCK });
}

/**
 * 删除对救助站的评价（普通用户）
 * @param {string} evaluationId - 评价ID
 * @returns {Promise} 删除结果
 */
function deleteRescueStationEvaluation(evaluationId) {
  return request.delete(`/users/common/rescuestation/delete/${evaluationId}`, {}, { useMock: CONFIG.USE_MOCK });
}

/**
 * 查看用户对商店的评价（商店）
 * @param {Object} params - 查询参数
 * @returns {Promise} 评价列表
 */
function getEvaluationsForShop(params = {}) {
  return request.get('/users/shop/view/evaluations', params, { useMock: CONFIG.USE_MOCK });
}

/**
 * 查看用户对医院的评价（医院）
 * @param {Object} params - 查询参数
 * @returns {Promise} 评价列表
 */
function getEvaluationsForHospital(params = {}) {
  return request.get('/users/hospital/view/evaluation', params, { useMock: CONFIG.USE_MOCK });
}

/**
 * 查看用户对救助站的评价（救助站）
 * @param {Object} params - 查询参数
 * @returns {Promise} 评价列表
 */
function getEvaluationsForRescue(params = {}) {
  return request.get('/users/rescuestation/view/evaluation', params, { useMock: CONFIG.USE_MOCK });
}

export default {
  evaluateShop,
  evaluateHospital,
  evaluateRescueStation,
  getShopEvaluations,
  getHospitalEvaluations,
  getSelfHospitalEvaluations,
  getRescueStationEvaluations,
  deleteShopEvaluation,
  deleteHospitalEvaluation,
  deleteRescueStationEvaluation,
  getEvaluationsForShop,
  getEvaluationsForHospital,
  getEvaluationsForRescue
};